# Docker Environment Configuration for UpKeepPro
# Copy this file to .env.docker.local and modify as needed

# MongoDB Configuration
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
MONGO_INITDB_DATABASE=upkeeppro
MONGO_URI=********************************************************************

# Backend Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=ti2v3uZardPlOuoVie4k/D9KNMEH4S9DWFR/Oi+O114=
FRONTEND_URL=http://localhost:3000

# Frontend Configuration
VITE_API_URL=http://localhost:5000

# Ollama Configuration (for AI chatbot)
OLLAMA_HOST=0.0.0.0
OLLAMA_PORT=11434

# Security Configuration
SSL_CERT_PATH=/etc/nginx/ssl/nginx.crt
SSL_KEY_PATH=/etc/nginx/ssl/nginx.key

# Development vs Production
COMPOSE_PROJECT_NAME=upkeeppro
COMPOSE_FILE=docker-compose.yml
