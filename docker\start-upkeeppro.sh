#!/bin/bash

# UpKeepPro Docker Startup Script
# This script helps you start the entire UpKeepPro application stack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1 && ! docker compose version > /dev/null 2>&1; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    print_success "Docker Compose is available"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p uploads
    mkdir -p docker/ssl
    print_success "Directories created"
}

# Function to pull Ollama model (optional)
setup_ollama() {
    read -p "Do you want to set up Ollama for AI chatbot? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Setting up Ollama..."
        docker-compose up -d ollama
        print_status "Waiting for Ollama to start..."
        sleep 30
        print_status "Pulling TinyLlama model..."
        docker-compose exec ollama ollama pull tinyllama
        print_success "Ollama setup completed"
    else
        print_warning "Skipping Ollama setup. AI chatbot will not be available."
    fi
}

# Function to start the application
start_application() {
    print_status "Starting UpKeepPro application stack..."
    
    # Build and start all services
    docker-compose up -d --build
    
    print_status "Waiting for services to be healthy..."
    
    # Wait for MongoDB
    print_status "Waiting for MongoDB..."
    timeout 60 bash -c 'until docker-compose exec mongodb mongosh --eval "db.adminCommand(\"ping\")" > /dev/null 2>&1; do sleep 2; done'
    print_success "MongoDB is ready"
    
    # Wait for Backend
    print_status "Waiting for Backend..."
    timeout 60 bash -c 'until curl -f http://localhost:5000/api/users/stats > /dev/null 2>&1; do sleep 2; done'
    print_success "Backend is ready"
    
    # Wait for Frontend
    print_status "Waiting for Frontend..."
    timeout 60 bash -c 'until curl -f http://localhost:3000 > /dev/null 2>&1; do sleep 2; done'
    print_success "Frontend is ready"
    
    # Wait for Nginx
    print_status "Waiting for Nginx..."
    timeout 60 bash -c 'until curl -f http://localhost:80/health > /dev/null 2>&1; do sleep 2; done'
    print_success "Nginx is ready"
}

# Function to show application URLs
show_urls() {
    echo
    print_success "🎉 UpKeepPro is now running!"
    echo
    echo "📱 Application URLs:"
    echo "   Frontend (HTTP):  http://localhost:3000"
    echo "   Frontend (HTTPS): https://localhost (with self-signed cert)"
    echo "   Backend API:      http://localhost:5000"
    echo "   MongoDB:          mongodb://localhost:27017"
    echo
    echo "🔐 Default MongoDB Credentials:"
    echo "   Username: admin"
    echo "   Password: password123"
    echo "   Database: upkeeppro"
    echo
    echo "🤖 AI Chatbot (if enabled):"
    echo "   Ollama API:       http://localhost:11434"
    echo
    echo "📊 Useful Commands:"
    echo "   View logs:        docker-compose logs -f"
    echo "   Stop services:    docker-compose down"
    echo "   Restart:          docker-compose restart"
    echo "   Clean up:         docker-compose down -v --remove-orphans"
    echo
}

# Main execution
main() {
    echo "🚀 Starting UpKeepPro Docker Setup"
    echo "=================================="
    
    check_docker
    check_docker_compose
    create_directories
    start_application
    setup_ollama
    show_urls
    
    print_success "Setup completed successfully!"
}

# Run main function
main "$@"
