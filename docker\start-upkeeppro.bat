@echo off
setlocal enabledelayedexpansion

REM UpKeepPro Docker Startup Script for Windows
REM This script helps you start the entire UpKeepPro application stack

echo.
echo 🚀 Starting UpKeepPro Docker Setup
echo ==================================
echo.

REM Check if Docker is running
echo [INFO] Checking Docker status...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)
echo [SUCCESS] Docker is running

REM Check if Docker Compose is available
echo [INFO] Checking Docker Compose...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker Compose is not available. Please install Docker Compose.
        pause
        exit /b 1
    )
)
echo [SUCCESS] Docker Compose is available

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "uploads" mkdir uploads
if not exist "docker\ssl" mkdir docker\ssl
echo [SUCCESS] Directories created

REM Start the application
echo [INFO] Starting UpKeepPro application stack...
docker-compose up -d --build

if errorlevel 1 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)

echo [INFO] Waiting for services to be healthy...

REM Wait for MongoDB
echo [INFO] Waiting for MongoDB...
:wait_mongo
timeout /t 2 /nobreak >nul
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" >nul 2>&1
if errorlevel 1 goto wait_mongo
echo [SUCCESS] MongoDB is ready

REM Wait for Backend
echo [INFO] Waiting for Backend...
:wait_backend
timeout /t 2 /nobreak >nul
curl -f http://localhost:5000/api/users/stats >nul 2>&1
if errorlevel 1 goto wait_backend
echo [SUCCESS] Backend is ready

REM Wait for Frontend
echo [INFO] Waiting for Frontend...
:wait_frontend
timeout /t 2 /nobreak >nul
curl -f http://localhost:3000 >nul 2>&1
if errorlevel 1 goto wait_frontend
echo [SUCCESS] Frontend is ready

REM Wait for Nginx
echo [INFO] Waiting for Nginx...
:wait_nginx
timeout /t 2 /nobreak >nul
curl -f http://localhost:80/health >nul 2>&1
if errorlevel 1 goto wait_nginx
echo [SUCCESS] Nginx is ready

REM Ask about Ollama setup
echo.
set /p setup_ollama="Do you want to set up Ollama for AI chatbot? (y/n): "
if /i "!setup_ollama!"=="y" (
    echo [INFO] Setting up Ollama...
    docker-compose up -d ollama
    echo [INFO] Waiting for Ollama to start...
    timeout /t 30 /nobreak >nul
    echo [INFO] Pulling TinyLlama model...
    docker-compose exec ollama ollama pull tinyllama
    echo [SUCCESS] Ollama setup completed
) else (
    echo [WARNING] Skipping Ollama setup. AI chatbot will not be available.
)

REM Show application URLs
echo.
echo [SUCCESS] 🎉 UpKeepPro is now running!
echo.
echo 📱 Application URLs:
echo    Frontend (HTTP):  http://localhost:3000
echo    Frontend (HTTPS): https://localhost (with self-signed cert)
echo    Backend API:      http://localhost:5000
echo    MongoDB:          mongodb://localhost:27017
echo.
echo 🔐 Default MongoDB Credentials:
echo    Username: admin
echo    Password: password123
echo    Database: upkeeppro
echo.
echo 🤖 AI Chatbot (if enabled):
echo    Ollama API:       http://localhost:11434
echo.
echo 📊 Useful Commands:
echo    View logs:        docker-compose logs -f
echo    Stop services:    docker-compose down
echo    Restart:          docker-compose restart
echo    Clean up:         docker-compose down -v --remove-orphans
echo.
echo [SUCCESS] Setup completed successfully!
echo.
pause
