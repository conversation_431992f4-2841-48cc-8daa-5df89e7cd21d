# UpKeepPro Docker Management Makefile

.PHONY: help build up down restart logs clean status health setup

# Default target
help:
	@echo "🐳 UpKeepPro Docker Management"
	@echo "=============================="
	@echo ""
	@echo "Available commands:"
	@echo "  make setup     - Initial setup and start all services"
	@echo "  make build     - Build all Docker images"
	@echo "  make up        - Start all services"
	@echo "  make down      - Stop all services"
	@echo "  make restart   - Restart all services"
	@echo "  make logs      - View all logs"
	@echo "  make status    - Check service status"
	@echo "  make health    - Check service health"
	@echo "  make clean     - Clean up containers and volumes"
	@echo "  make ollama    - Setup Ollama AI service"
	@echo ""
	@echo "Service-specific commands:"
	@echo "  make logs-backend   - View backend logs"
	@echo "  make logs-frontend  - View frontend logs"
	@echo "  make logs-mongodb   - View MongoDB logs"
	@echo "  make logs-nginx     - View Nginx logs"
	@echo ""

# Initial setup
setup:
	@echo "🚀 Setting up UpKeepPro..."
	@mkdir -p uploads docker/ssl
	@docker-compose up -d --build
	@echo "⏳ Waiting for services to be ready..."
	@sleep 30
	@make health
	@echo "✅ Setup completed!"
	@make status

# Build all images
build:
	@echo "🔨 Building Docker images..."
	@docker-compose build

# Start all services
up:
	@echo "🚀 Starting UpKeepPro services..."
	@docker-compose up -d

# Stop all services
down:
	@echo "🛑 Stopping UpKeepPro services..."
	@docker-compose down

# Restart all services
restart:
	@echo "🔄 Restarting UpKeepPro services..."
	@docker-compose restart

# View all logs
logs:
	@docker-compose logs -f

# Service-specific logs
logs-backend:
	@docker-compose logs -f backend

logs-frontend:
	@docker-compose logs -f frontend

logs-mongodb:
	@docker-compose logs -f mongodb

logs-nginx:
	@docker-compose logs -f nginx

logs-ollama:
	@docker-compose logs -f ollama

# Check service status
status:
	@echo "📊 Service Status:"
	@docker-compose ps
	@echo ""
	@echo "🌐 Access URLs:"
	@echo "  Frontend:     http://localhost:3000"
	@echo "  Frontend SSL: https://localhost"
	@echo "  Backend API:  http://localhost:5000"
	@echo "  MongoDB:      mongodb://localhost:27017"
	@echo "  Ollama:       http://localhost:11434"

# Health check
health:
	@echo "🏥 Checking service health..."
	@echo -n "MongoDB: "
	@curl -s http://localhost:27017 > /dev/null && echo "✅ Healthy" || echo "❌ Unhealthy"
	@echo -n "Backend: "
	@curl -s http://localhost:5000/api/users/stats > /dev/null && echo "✅ Healthy" || echo "❌ Unhealthy"
	@echo -n "Frontend: "
	@curl -s http://localhost:3000 > /dev/null && echo "✅ Healthy" || echo "❌ Unhealthy"
	@echo -n "Nginx: "
	@curl -s http://localhost:80/health > /dev/null && echo "✅ Healthy" || echo "❌ Unhealthy"

# Setup Ollama
ollama:
	@echo "🤖 Setting up Ollama AI service..."
	@docker-compose up -d ollama
	@echo "⏳ Waiting for Ollama to start..."
	@sleep 30
	@echo "📥 Pulling TinyLlama model..."
	@docker-compose exec ollama ollama pull tinyllama
	@echo "✅ Ollama setup completed!"

# Clean up
clean:
	@echo "🧹 Cleaning up Docker resources..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "✅ Cleanup completed!"

# Development helpers
dev-backend:
	@echo "🔧 Starting backend in development mode..."
	@docker-compose up -d mongodb
	@cd backend && npm run dev

dev-frontend:
	@echo "🔧 Starting frontend in development mode..."
	@cd frontend && npm run dev

# Database management
db-shell:
	@echo "🗄️ Connecting to MongoDB shell..."
	@docker-compose exec mongodb mongosh -u admin -p password123 upkeeppro

db-backup:
	@echo "💾 Creating database backup..."
	@docker-compose exec mongodb mongodump --uri="**********************************************************************" --out=/tmp/backup
	@docker cp upkeeppro-mongodb:/tmp/backup ./backup-$(shell date +%Y%m%d-%H%M%S)
	@echo "✅ Backup created!"

# Update and rebuild
update:
	@echo "🔄 Updating and rebuilding services..."
	@docker-compose down
	@docker-compose build --no-cache
	@docker-compose up -d
	@echo "✅ Update completed!"
