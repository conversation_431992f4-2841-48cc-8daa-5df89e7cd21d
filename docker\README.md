# 🐳 UpKeepPro Docker Setup

Complete Docker containerization for the UpKeepPro application with MongoDB, Nginx reverse proxy, and optional Ollama AI service.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │    Frontend     │    │    Backend      │
│  (Reverse Proxy)│◄──►│   (React/Vite)  │◄──►│  (Node.js/API)  │
│   Port: 80/443  │    │   Port: 3000    │    │   Port: 5000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐             │
         └─────────────►│    MongoDB      │◄────────────┘
                        │   Port: 27017   │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │     Ollama      │
                        │ (AI Service)    │
                        │  Port: 11434    │
                        └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- At least 4GB RAM available
- Ports 80, 443, 3000, 5000, 11434, 27017 available

### Option 1: Automated Setup (Recommended)

**For Windows:**
```cmd
cd docker
start-upkeeppro.bat
```

**For Linux/macOS:**
```bash
cd docker
chmod +x start-upkeeppro.sh
./start-upkeeppro.sh
```

### Option 2: Manual Setup

1. **Start all services:**
```bash
docker-compose up -d --build
```

2. **Check service status:**
```bash
docker-compose ps
```

3. **View logs:**
```bash
docker-compose logs -f
```

## 📋 Services

### 🗄️ MongoDB
- **Image:** `mongo:7.0`
- **Port:** `27017`
- **Credentials:** admin/password123
- **Database:** upkeeppro
- **Volume:** `mongodb_data`

### 🖥️ Backend (Node.js)
- **Build:** Custom Dockerfile
- **Port:** `5000`
- **Environment:** Production
- **Health Check:** `/api/users/stats`

### 🌐 Frontend (React)
- **Build:** Multi-stage with Nginx
- **Port:** `3000`
- **Build Tool:** Vite
- **Health Check:** Root endpoint

### 🔄 Nginx (Reverse Proxy)
- **Port:** `80` (HTTP), `443` (HTTPS)
- **SSL:** Self-signed certificate
- **Features:** Rate limiting, compression, security headers

### 🤖 Ollama (AI Service)
- **Image:** `ollama/ollama:latest`
- **Port:** `11434`
- **Model:** TinyLlama (optional)
- **Volume:** `ollama_data`

## 🌐 Access URLs

| Service | URL | Description |
|---------|-----|-------------|
| Frontend | http://localhost:3000 | Direct frontend access |
| Frontend | https://localhost | Nginx proxy (HTTPS) |
| Backend API | http://localhost:5000 | Direct API access |
| MongoDB | mongodb://localhost:27017 | Database connection |
| Ollama | http://localhost:11434 | AI service API |

## 🔧 Configuration

### Environment Variables

Create `.env.docker.local` from `.env.docker` template:

```bash
cp .env.docker .env.docker.local
# Edit .env.docker.local with your settings
```

### MongoDB Configuration
```yaml
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
MONGO_INITDB_DATABASE=upkeeppro
```

### Security Settings
- Self-signed SSL certificates generated automatically
- Rate limiting enabled (10 req/s for API, 5 req/m for login)
- Security headers configured
- Non-root users in containers

## 📊 Monitoring & Logs

### View all logs:
```bash
docker-compose logs -f
```

### View specific service logs:
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongodb
docker-compose logs -f nginx
```

### Check service health:
```bash
docker-compose ps
```

## 🛠️ Management Commands

### Start services:
```bash
docker-compose up -d
```

### Stop services:
```bash
docker-compose down
```

### Restart services:
```bash
docker-compose restart
```

### Rebuild and start:
```bash
docker-compose up -d --build
```

### Clean up everything:
```bash
docker-compose down -v --remove-orphans
docker system prune -a
```

### Scale services:
```bash
docker-compose up -d --scale backend=2
```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts:**
```bash
# Check what's using the ports
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000
```

2. **MongoDB connection issues:**
```bash
# Check MongoDB logs
docker-compose logs mongodb

# Connect to MongoDB shell
docker-compose exec mongodb mongosh -u admin -p password123
```

3. **Frontend build issues:**
```bash
# Rebuild frontend only
docker-compose build frontend
docker-compose up -d frontend
```

4. **Backend API issues:**
```bash
# Check backend logs
docker-compose logs backend

# Test API endpoint
curl http://localhost:5000/api/users/stats
```

### Performance Optimization

1. **Increase Docker resources:**
   - Memory: 4GB minimum, 8GB recommended
   - CPU: 2 cores minimum, 4 cores recommended

2. **Enable BuildKit:**
```bash
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1
```

## 🔒 Security Considerations

### Production Deployment

1. **Change default passwords:**
   - MongoDB admin password
   - JWT secret key

2. **Use proper SSL certificates:**
   - Replace self-signed certificates
   - Configure proper domain names

3. **Environment variables:**
   - Use Docker secrets for sensitive data
   - Don't commit `.env` files

4. **Network security:**
   - Use custom networks
   - Implement proper firewall rules

### Development vs Production

The current setup is optimized for development. For production:

1. Use environment-specific compose files
2. Implement proper logging and monitoring
3. Use external databases and load balancers
4. Implement backup strategies

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [MongoDB Docker Hub](https://hub.docker.com/_/mongo)
- [Nginx Docker Hub](https://hub.docker.com/_/nginx)
- [Ollama Documentation](https://ollama.ai/)
