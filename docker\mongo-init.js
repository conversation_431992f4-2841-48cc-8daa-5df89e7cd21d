// MongoDB initialization script for UpKeepPro
// This script runs when the MongoDB container starts for the first time

// Switch to the upkeeppro database
db = db.getSiblingDB('upkeeppro');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email', 'role'],
      properties: {
        username: {
          bsonType: 'string',
          description: 'Username is required and must be a string'
        },
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'Email must be a valid email address'
        },
        role: {
          bsonType: 'string',
          enum: ['CC', 'REPI', 'RM', 'FORM', 'RLOG', 'CAR', 'REP', 'STAG'],
          description: 'Role must be one of the predefined values'
        }
      }
    }
  }
});

db.createCollection('tasks');
db.createCollection('events');
db.createCollection('notifications');
db.createCollection('messages');
db.createCollection('documents');
db.createCollection('tests');
db.createCollection('toolings');
db.createCollection('locations');
db.createCollection('placements');
db.createCollection('responsibles');

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 });
db.users.createIndex({ role: 1 });

db.tasks.createIndex({ assignedTo: 1 });
db.tasks.createIndex({ createdBy: 1 });
db.tasks.createIndex({ status: 1 });
db.tasks.createIndex({ dueDate: 1 });

db.events.createIndex({ userId: 1 });
db.events.createIndex({ date: 1 });
db.events.createIndex({ status: 1 });

db.notifications.createIndex({ userId: 1 });
db.notifications.createIndex({ read: 1 });
db.notifications.createIndex({ createdAt: 1 });

db.messages.createIndex({ senderId: 1 });
db.messages.createIndex({ receiverId: 1 });
db.messages.createIndex({ createdAt: 1 });

db.documents.createIndex({ createdBy: 1 });
db.documents.createIndex({ category: 1 });
db.documents.createIndex({ status: 1 });

db.tests.createIndex({ createdBy: 1 });
db.tests.createIndex({ category: 1 });
db.tests.createIndex({ status: 1 });

// Create a default admin user (optional)
// Note: In production, you should create users through your application
print('MongoDB initialization completed for UpKeepPro database');
print('Collections created with indexes for optimal performance');
print('Database is ready for the UpKeepPro application');
